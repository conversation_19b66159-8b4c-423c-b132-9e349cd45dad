# Use the official Python slim image as the parent image
FROM python:3.11-slim AS base

# Copy the uv tool from the specified image
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/

# Set the working directory
WORKDIR /app

# Install system dependencies using apt-get
# build-essential: Compiler and tools for building software
# libcairo2: Cairo graphics library
# libpango-1.0-0: Pango text rendering library
# libpangocairo-1.0-0: Pango Cairo integration
# libgdk-pixbuf2.0-0: GDK-Pixbuf library for image loading
# libffi-dev: Foreign Function Interface development libraries
# shared-mime-info: Shared MIME database
# libxml2-dev: XML parsing library
# libxslt-dev: XSLT transformation library
# libgbm1: GBM library (for GPU acceleration)
# fonts-liberation: Liberation fonts
# fonts-roboto: Roboto fonts
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    libcairo2 \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libgdk-pixbuf2.0-0 \
    libffi-dev \
    shared-mime-info \
    libxml2-dev \
    libxslt-dev \
    libgbm1 \
    fonts-liberation \
    fonts-roboto \
    curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Stage 1: Install Python dependencies
FROM base AS dependencies

# Copy only the files needed for dependency installation
COPY pyproject.toml poetry.lock* uv.lock ./

# Install Python dependencies using uv
RUN uv sync --frozen --no-cache

# Stage 2: Install Playwright dependencies
FROM dependencies AS playwright

# Install Playwright Chromium with system dependencies
RUN uv run playwright install chromium --with-deps

# Stage 3: Copy application code
FROM playwright AS app

# Copy the rest of the application code
COPY . .

# Expose the application port
EXPOSE 8000


# Run the application using gunicorn
CMD sh -c "uv run gunicorn -w 1 -k uvicorn.workers.UvicornWorker -b 0.0.0.0:8000 main:app"