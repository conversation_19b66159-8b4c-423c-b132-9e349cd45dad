try:
    import streamlit as st
except Exception as e:
    import os
    os.system("pip install streamlit")
finally:
    import streamlit

# Configure the page layout
st.set_page_config(page_title="Chat", layout="wide")
SPIN_STATES = {

    "EntryStage": {
        "requirements": [
            {
                "name": "Greeting and Initiation",
                "required" : {
                    # "greet_user" : "not_sent",
                    "mention_divine_aesthetics": "not_sent",
                    "mention_you_are_an_ai_assistant": "not_sent"
                },
                "spin_state_type":"send_info"
            }
        ],
    },

    "BasicInfo": {
        "requirements": [
            {
                "name": "Gather Basic Information",
                "required" : {
                    # "customer_background": "not found",
                    "weight":"not_found",
                    "height": "not_found",
                    # "age": "not_found",
                },
                "spin_state_type":"gather_info"
            }
        ],
        
    },

    "BookingsInfo": {
        "requirements": [
            {
                "name": "Gather Booking Information",
                "required" : {
                    "available_date": "not_found",
                },
                "spin_state_type":"gather_info"
            }
        ],

    },
    "BookedInfo": {
        "requirements": [
            {
                "name": "Inform User of Booking",
                "required" : {
                    "booking_information": "not_sent",
                },
                "spin_state_type":"send_info"
            }
        ],

    },
    "ConversationEnd": {
        "requirements": [
            {
                "name":"Customer Satisfaction",
                "required": {
                    "customer_satisfaction":"not_found"
                },
                "spin_state_type":"gather_info"
            }
        ],
    }
}

# Initialize session state for chat messages if not already present
if "messages" not in st.session_state:
    st.session_state["messages"] = []
if "previous_summary" not in st.session_state:
    st.session_state["previous_summary"] = "This is a New Conversation. "
if "spin_states" not in st.session_state:
    st.session_state["spin_states"] = SPIN_STATES
if "current_spin_state" not in st.session_state:
    st.session_state["current_spin_state"] = "EntryStage"

from src.streamlitdemo.chatdemo.respond import (
    generate_response,
    MsgRequest,
    calculate_summary,
    gather_information_for_cur_state,
    all_requirements_fullfilled
)



# Define three columns: left for chat, middle for document management, right for TOC

# ------------------------
# Left Column: Enhanced Chat Interface
# ------------------------
st.header("Chat Interface")

# Display chat history using experimental chat components if available
# Each message is stored as a dictionary with 'role' and 'content'
for message in st.session_state["messages"]:
    if message["role"] == "user":
        st.chat_message("user").write(message["content"])
    else:
        st.chat_message("assistant").write(message["content"])

# Chat input: When the user sends a message, it's appended to the session state,
# and a placeholder AI response is generated.
user_input = st.chat_input("Type your message here")
if user_input:
    # Append user message
    st.session_state["messages"].append({"role": "user", "content": user_input})

    # Here, you would normally process the input with an AI model.
    # For this example, we'll simulate a response.
    # ai_response = "This is a placeholder response from the AI."

    print(f'xx***{st.session_state["previous_summary"]}***xx')

    current_spin_state = st.session_state["current_spin_state"]
    spin_states = st.session_state["spin_states"]

    # if current_spin_state == "End of Problem":
    #     current_spin_state = "Situation"
    
    current_index = list(spin_states.keys()).index(current_spin_state)
    next_index = (current_index + 1) % len(spin_states)
    next_spin_state = list(spin_states.keys())[next_index]

    information_gathering:dict = gather_information_for_cur_state(current_spin_state, spin_states, user_input, st.session_state["previous_summary"])
    if all_requirements_fullfilled(information_gathering):
        st.session_state["current_spin_state"] = next_spin_state
        current_spin_state = st.session_state["current_spin_state"]
        spin_states = st.session_state["spin_states"]
        # Get the next spin state in a cyclic manner using modulo
        current_index = list(spin_states.keys()).index(current_spin_state)
        next_index = (current_index + 1) % len(spin_states)
        next_spin_state = list(spin_states.keys())[next_index]
        information_gathering:dict = gather_information_for_cur_state(current_spin_state, spin_states, user_input, st.session_state["previous_summary"])
        print("INFO_IF:", information_gathering)
    else:
        print("OUT_IF", information_gathering)
    ai_response = generate_response(
        MsgRequest(
            customer_id=0,
            message=user_input,
            previous_summary=str(st.session_state["messages"][-5:]),
            current_spin_state=current_spin_state,
            next_spin_state=next_spin_state,
            info_gathering = information_gathering,
            # json_data_with_fields_str_formatted=str(information_gathering),
            spin_states=st.session_state["spin_states"]
        )
    )
    st.session_state["messages"].append({"role": "assistant", "content": ai_response})

    st.session_state["previous_summary"] = calculate_summary(
        st.session_state["previous_summary"],
        user_message=user_input,
        ai_response=ai_response,
    )

    # Update the gahtered information
    st.session_state["spin_states"][current_spin_state]["requirements"][0] = information_gathering

    # Update the current spin state if the data of the current state is gathered

    # if all_requirements_fullfilled(information_gathering):
    #     st.session_state["current_spin_state"] = next_spin_state

    # Rerun to update the chat interface with the new messages
    st.rerun()
