import streamlit as st 

# Configure the page layout
st.set_page_config(page_title="Chat", layout="wide")
# Initialize session state for chat messages if not already present
if "messages" not in st.session_state:
    st.session_state["messages"] = []

from src.streamlitdemo.chatdemo.respond import (
    generate_response_openai,
    MsgRequest,
    # calculate_summary,
    # gather_information_for_cur_state,
    # all_requirements_fullfilled
)



# Define three columns: left for chat, middle for document management, right for TOC

# ------------------------
# Left Column: Enhanced Chat Interface
# ------------------------
st.header("Chat Interface")

# Display chat history using experimental chat components if available
# Each message is stored as a dictionary with 'role' and 'content'
for message in st.session_state["messages"]:
    if message["role"] == "user":
        st.chat_message("user").write(message["content"])
    else:
        st.chat_message("assistant").write(message["content"])

# Chat input: When the user sends a message, it's appended to the session state,
# and a placeholder AI response is generated.
user_input = st.chat_input("Type your message here")
if user_input:
    # Append user message # is being added below
    st.session_state["messages"].append({"role": "user", "content": user_input})

    # Here, you would normally process the input with an AI model.
    # For this example, we'll simulate a response.
    # ai_response = "This is a placeholder response from the AI."

   

    ai_response, source_nodes = generate_response_openai(
        MsgRequest(
            customer_id="10",
            message=user_input,
            # chat_history=st.session_state["messages"],
            previous_summary=st.session_state["messages"][-5:-1],
            # current_spin_state=current_spin_state,
            # next_spin_state=next_spin_state,
            # info_gathering = information_gathering,
            # # json_data_with_fields_str_formatted=str(information_gathering),
            # spin_states=st.session_state["spin_states"]
        ),
        SYS_PROMPT="""Background: 
You are an experienced and empathetic Cosmetic Surgery Treatment Consultant at Divine Aesthetics.

Role: 
Your role is to address patient inquiries with the goal of driving them toward an in-person consultation via online booking for new or inquiry-related patients. Do not suggest a consultation immediately. Aim to provide at least 2–3 helpful responses before naturally leading toward booking. For returning patients, provide necessary answers to their queries. Provide tailored answers based solely on the information from the `search_database` tool. Avoid offering facts or figures outside of this reference.

Style: 
Do not discuss pricing unless the patient asks; if they do, first identify the procedure,service or product, then offer a simple, direct explanation or range, reassuring them about the value and safety of the services based on Database information. Ask short, relevant follow-up questions only when needed to clarify the patient’s needs or gently guide them toward the right treatment. Avoid using the term 'database' in your responses.  Use an active voice and a personal, direct manner.


Response Length: 
{response_mode_str} ensuring that all necessary details are covered intelligently, while remaining natural and empathetic. Divide relevant information into chunks seperated by `\n\n\n` for better clarity.


Things to avoid: 
Avoid responding to queries on out of topic situations like movies, music, politics etc. You are not an encyclopedia—focus on what the clinic can offer regarding their specific needs.
Ask follow-ups only when necessary to clarify patient needs or guide them toward the right procedure. When addressing risks associated with procedures, avoid naming specific diseases—instead, mention that there could be some risks in certain cases and advise discussing these in detail during an in-person consultation. Do not provide generalized information; tailor your response to what the clinic can do for the patient's query.

Language: 
Respond in {language} language.

Instructions for Handling Difficult Situations: 
If unsure or if a highly specific medical concern is raised, acknowledge that more details are best discussed in an in-person consultation. 
When patients provides an image create a issue ticket with proper information and respond that our expert will reach out to them with the necessary details.


References: 
Product/Services/Procedure Identified: {identified_product}  

Output:"""
    )
    print(f"Text:z`{ai_response}`")
    st.session_state["messages"].append({"role": "assistant", "content": ai_response})

    # st.session_state["previous_summary"] = calculate_summary(
    #     st.session_state["previous_summary"],
    #     user_message=user_input,
    #     ai_response=ai_response,
    # )

    # Update the gahtered information
    # st.session_state["spin_states"][current_spin_state]["requirements"][0] = information_gathering

    # Update the current spin state if the data of the current state is gathered

    # if all_requirements_fullfilled(information_gathering):
    #     st.session_state["current_spin_state"] = next_spin_state

    # Rerun to update the chat interface with the new messages
    st.rerun()
