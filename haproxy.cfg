global
    log stdout format raw local0 info
    maxconn 2000
    # Changed to use a directory that HAProxy can write to
    #stats socket /var/run/haproxy.sock mode 777 level admin
    stats timeout 2m

defaults
    mode http
    log global
    option httplog
    option dontlognull
    timeout connect 5000ms
    timeout client 50000ms
    timeout server 50000ms

frontend http_front
    bind *:80
    default_backend http_back

backend http_back
    balance roundrobin
    option forwardfor
    option http-server-close
    option httpchk GET /health
    default-server check inter 60000 rise 2 fall 3
    server-template app 10 app:8000 check resolvers docker init-addr none

resolvers docker
    nameserver dns 127.0.0.11:53
    resolve_retries 3
    timeout retry 1s
    hold valid 10s

listen stats
    bind *:1936
    stats enable
    stats uri /
    stats hide-version
    stats auth admin:admin
    stats refresh 5s
