


from .resolve_llm import resolve_llm
from src.v2.KB.qdrant.qdrant_client import Qdrant_Call, QdrantConfig
import asyncio
from .get_db import db 
from .phrases_to_avoid import phrases_to_avoid
import openai
from .track_time import time_it
from typing import Optional, Any

@time_it
def generate_query(data) -> str:
    """Uses Summary and Latest user message to generate a query using a LLM Call"""

    latest_message_str = data["latest_message_str"]
    summary_str = data["summary_str"]

    try:
        SYS_PROMPT = """
Given a User Conversation, generate a concise Retriever Query that accurately represents the solution for what the user is asking about.
Guidelines:
- No Hallucination: Stick strictly to the provided information. Do not infer or assume details.
- Concise & Precise: Keep the query short while capturing the key intent of the user's request.
- Context-Aware: Ensure the retriever query reflects the true nature of the user's query.
- If the user asks for information, the retriever query should directly reflect that.
- If the user expresses a problem, the retriever query should focus on the solution.
- If the user expresses an emotion (e.g., fear, confusion), the retriever query should focus on ways to ease that emotion.

Example:
```
Example Input 1:
User's Query: "The user is agreeing to proceed to payment for breast implants surgery"

Example Output 1:
Output: "breast implants surgery payment information"

Example Input 2:
User's Query: "The user says they are scared about their upcoming surgery"

Example Output 2:
Output: "ways to ease anxiety before surgery"
```

Output:"""

        messages  = [{"role":"system", "content":SYS_PROMPT}] + summary_str[-2:] + [{"role":"user", "content":latest_message_str}]

        response = openai.OpenAI().chat.completions.create(
            model="gpt-4o",
            messages=messages
        )

        query_ = response.choices[0].message.content

        return query_
    except Exception as e:
        print(e)
        return "No Query Generated"



@time_it
def generate_answer_from_query(query:str, qd_client,current_user:Optional[Any] = None) -> str:
    print("QUERY", query)
    # Answer from the database shoud be elaborated so use topk >= 10 
    default_prompt = "Do not rely on your own knowledge. \nProvide an answer to the question : \n Question : `{query} mentioned in the document`"

    if current_user:
        top=current_user.db.settings.find_one({"name": "env"}).get("qdrant_config").get("topk")
        print("top",top)
        prompt = current_user.db.prompt.find_one({"name": "retriever_prompt"}).get("text", default_prompt)
    else:
        top=5
        prompt = default_prompt

    prompt = prompt.format(query=query)
    # print("prompt", prompt)
    response = asyncio.run(qd_client.query_engine(query=prompt, top_k=top, metadata=None))
    # phrase_to_avoid = phrases_to_avoid({"answer":response.response})
    return {"reply":response.response}, response.source_nodes

@time_it
def generate_answer_from_toc(query:str, qd_client,metadata:dict,current_user:Optional[Any] = None) -> str:
    print("QUERY", query)
    default_prompt = "Do not rely on your own knowledge. \nProvide an answer to the question : \n Question : `{query} mentioned in the document`"
    if current_user:
        top=current_user.db.settings.find_one({"name": "env"}).get("qdrant_config").get("topk")
        print("top",top)
        prompt = current_user.db.prompt.find_one({"name": "retriever_prompt"}).get("text", default_prompt)
    else:
        top=5
        prompt = default_prompt
    prompt = prompt.format(query=query)
    response = asyncio.run(qd_client.query_engine(query=prompt, top_k=top, metadata=metadata))
    return {"reply":response.response}, response.source_nodes