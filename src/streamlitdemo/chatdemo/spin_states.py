from .resolve_llm import resolve_llm
from .get_db import db 
import json
from .track_time import time_it

@time_it
def gather_information_for_cur_state(current_spin_state, spin_states, user_input, user_summary):
    print(f'current_spin_state: {current_spin_state}')
    print(f'spin_states: {spin_states}')
    print(f'user_input: {user_input}')

    current_state = spin_states[current_spin_state]
    
    PREV_INFO = current_state["requirements"][0]
    INFO_TYPE =  PREV_INFO.pop("spin_state_type")
    INFO_NAME =  PREV_INFO.get("name")

    prompt_ = """Update Information present by gahtering from the user's input or seeing if the information has been sent to the user for the current state: {current_spin_state}.\n
Conversation Summary: 
```{user_summary}```

User's Input: 
```{user_input}```

Previous Information: 
```{PREV_INFO}```

If information for certain field is either found or has been sent, update it accordingly. else keep it as `not_found` or `not_sent`.

Updated Information:""".format(
            current_spin_state=current_spin_state, user_input=user_input,user_summary=user_summary, PREV_INFO=current_state["requirements"][0]
    )

    response = resolve_llm(model_name="gpt-4o-mini").complete(
        prompt=prompt_
    )
    import re
    match = re.search(r'\{(?:[^{}]|\{(?:[^{}]|\{[^{}]*\})*\})*\}', response.text.replace("python","").replace("`","").replace("json","").replace("'",'"'))
    if match:
        json_data = match.group(0)
    else:
        json_data = ""


    # text_d = json.loads(json_data)

    # text_ = response.text.replace("python","").replace("`","").replace("json","").replace("'",'"')
    # print(f'text_: {text_}')
    fields_in_current_state = spin_states[current_spin_state]["requirements"][0].get("required", {}).keys()
    print("GATHER_INFO_FIELDS:", fields_in_current_state)
    print("GATHER_INFO_P:", prompt_)
    print("GATHER_INFO:", json_data)

    try: 
        text_d = json.loads(json_data)
        # Create a new dictionary with only valid keys to avoid size changes during iteration
        text_d = {key: value for key, value in text_d.get("required").items() if key in list(fields_in_current_state)}
        text_d = {
            "required": text_d
        }
    except Exception as e: print(f"{e}\n\n{json_data}\n\n{response.text}");text_d = {}

    PREV_INFO["spin_state_type"] = INFO_TYPE
    PREV_INFO["name"] = INFO_NAME
    text_d["spin_state_type"] = INFO_TYPE
    text_d["name"] = INFO_NAME
    print("GATHER_INFO_NEW:", text_d)
    return text_d

@time_it
def all_requirements_fullfilled(info):

    """
    Requirements can be considered fullfilled if 70% of the required fields have been fullfilled
    Input:
    info:{
        "name":"StageName","required":{
                "field1":"not_found" or "not_sent",
                "field2":"not_found" or "not_sent",
                "field3":"Other values"
            }
    }

    total_no_of_fields = len(info.get(required).valus()) (T)

    total_not_fullfilled = total no of "not_found" or "not_sent" str in the str(info) (TnF)

    percentage = T - TnF / T * 100 

    if percentage > 0.6 return true else false.

    """

    required_fields = info.get("required", {})
    total_fields = len(required_fields)
    
    if total_fields == 0:
        return False  # No required fields means nothing can be fulfilled
    
    not_fulfilled_count = sum(1 for value in required_fields.values() if value in {"not_found", "not_sent"})
    fulfilled_percentage = (total_fields - not_fulfilled_count) / total_fields
    print(f"fulfilled_percentage: {fulfilled_percentage}")
    return fulfilled_percentage >= 0.4


    not_found = "not_found" in str(info) or "not_sent" in str(info)

    return not not_found

    # prompt =f"""Identify whether the context contains any information that is not found.
    # - if any field is `not_found` return NO 
    # - if all required fields are set to `found` return YES. 
    
    # Informtation:```{info}```
    
    # Output:"""
    # resp_ = resolve_llm(model_name="gpt-4o-mini").complete(prompt=prompt)
    # print("PROMPT:", prompt)
    # print("FULLFILL_REQ:", resp_.text)
    # return "yes" in resp_.text.lower()



