from typing import List

from .get_db import db
from .resolve_llm import resolve_llm

from .track_time import time_it

@time_it
def contextualize_messages(data) -> str:

    # if current_user.db.settings.find_one({"name": "env","contextualize":False}):
    #     return message
    latest_message_str = data["latest_message_str"]
    summary_str = data["summary_str"]


    prompt=db["prompt"].find_one({"name":"Contextualize"})["text"]
    model=db["prompt"].find_one({"name":"Contextualize"})["model"]
    
    try:
        llm = resolve_llm(model_name=model)
        return llm.complete(
            prompt.format(message=latest_message_str, chat_hist=summary_str),
            formatted=True
        ).text
    except Exception as e:
        raise e
    