from .resolve_llm import resolve_llm

from .get_db import db

from .track_time import time_it

@time_it
def phrases_to_avoid(data):
    answer = data["answer"]
    try:

        response = resolve_llm(model_name="gpt-4o").complete(
            prompt = """
Given the following information, extract phrases or words that give a negative sentiment or negative connotation, or reduce confidence in the answer.
Keep the phrases short and to the point. phrases must be present in the Information and do not add any additional information.

Example:
Information: ```Breast implants are generally considered safe for surgery, and there are no proven links to cancer or other health-related problems.However, specific types, such as tear-drop shaped implants, may be challenging to obtain in India due to long waiting times and low inventory ```
Output: ```generally, maybe```

Information: ```{answer}```
Output:""".format(answer=answer)
        )

        # model = language_prompt["model"]
        # llm= resolve_llm(model_name=model)
        # language_prompt=prompt_.format(question=latest_message_str)
        # response = llm.complete(prompt=language_prompt,formatted=True)
        print(response.text)
        return response.text
    except Exception as e:
        # loggers.error(f"Error detecting language: {e}") # same here fix this or remove this
        raise e

if __name__ == "__main__":
    phrases_to_avoid(
        {
            "answer": "Breast implants are considered safe for surgery, and there are no proven links to cancer or other health-related problems. However, specific types, such as tear-drop shaped implants, may be challenging to obtain in India due to long waiting times and low inventory. It's important for individuals considering breast implants to consult with healthcare professionals to address any concerns and ensure they are well-informed about the options available."
        }
    )