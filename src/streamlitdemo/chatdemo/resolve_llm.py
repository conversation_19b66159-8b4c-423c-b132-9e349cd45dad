from llama_index.llms.openai import OpenAI
from llama_index.llms.gemini import Gemini
import functools
import os

from .track_time import time_it
from .get_db import db

from dotenv import load_dotenv
load_dotenv()

@time_it
@functools.lru_cache(maxsize=128)
def resolve_llm(model_name: str, api_config: dict | None = None):
    """
    Resolve the LLM model based on the model name.
    """

    if not api_config:
        # api_config = {}
        api_config = db.settings.find_one(
            {"name": "env"}
        ).get("config")

    OPENAI_API_KEY = api_config.get("OPENAI_API_KEY", os.environ.get("OPENAI_API_KEY"))
    GOOGLE_API_KEY = api_config.get("GOOGLE_API_KEY", os.environ.get("GOOGLE_API_KEY"))

    if not model_name:
        return OpenAI(model="gpt-4o-mini", api_key=OPENAI_API_KEY)

    model_name = model_name.lower()

    if "gpt" in model_name:
        # print("OPENAI_API_KEY",OPENAI_API_KEY)

        return OpenAI(model=model_name, api_key=OPENAI_API_KEY)
    elif "gemini" in model_name:
        # print("GOOGLE_API_KEY",GOOGLE_API_KEY)
        return Gemini(model_name=model_name, api_key=GOOGLE_API_KEY)
    else:
        # Default to gpt-4o-mini if model name not recognized
        return OpenAI(model="gpt-4o-mini", api_key=OPENAI_API_KEY)
