
from fastapi import APIRouter, Depends, HTTPException, Body
from typing import Optional
from src.core.security import get_tenant_info
from src.models.user import UserTenantDB
from src.helper.logger import setup_new_logging
from src.v2.external_hooks.social_media_webhooks.client import get_sociar_client
from src.v2.external_hooks.social_media_webhooks.models import (
    FacebookMessageRequest, 
    InstagramMessageRequest,
    SocialMediaMessageRequest, 
    WhatsAppTemplate,
    SocialMediaResponse
)
from src.v2.external_hooks.social_media_webhooks.client import (
    send_facebook_message,
    send_instagram_message,
)

loggers = setup_new_logging(__name__)
router = APIRouter(tags=["Sociar APIs"], prefix="/sociar-media")

@router.post("/facebook/message-send")
async def facebook_message_send(
    request: SocialMediaMessageRequest,
    current_user: UserTenantDB = Depends(get_tenant_info)
) -> SocialMediaResponse:
    try:
        response = await send_facebook_message(
            request.profile_id,
            request.content,
            current_user
        )

        return SocialMediaResponse(
            success=True,
            data=response,
            message="Facebook message sent successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error sending Facebook message: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal Server Error")


@router.post("/instagram/message-send")
async def instagram_message_send(
    request: SocialMediaMessageRequest,
    current_user: UserTenantDB = Depends(get_tenant_info)
) -> SocialMediaResponse:
    try:
        response = await send_instagram_message(
            request.profile_id,
            request.content,
            current_user
        )

        return SocialMediaResponse(
            success=True,
            data=response,
            message="Instagram message sent successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error sending Instagram message: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal Server Error")


@router.post("/whatsapp/setup")
async def setup_sociar_api(
    api_token: str = Body(...),
    base_url: Optional[str] = Body("https://new-central-api.sociair.com/api"),
    current_user: UserTenantDB = Depends(get_tenant_info)
) -> SocialMediaResponse:
    """
    Set up Sociar API configuration in the database.

    Args:
        api_token: API token for authentication
        base_url: Base URL for the API
        current_user: Current user with database access

    Returns:
        Success message
    """
    try:
        # Check if settings already exist
        existing_settings = await current_user.async_db.settings.find_one({"name": "sociar_env"})

        settings = {
            "API_TOKEN": api_token,
            "BASE_URL": base_url
        }

        if existing_settings:
            # Update existing settings
            result = await current_user.async_db.settings.update_one(
                {"name": "sociar_env"},
                {"$set": settings}
            )

            if result.modified_count == 0:
                raise Exception("Failed to update Sociar settings")

            return SocialMediaResponse(
                success=True,
                message="Sociar API settings updated successfully"
            )
        else:
            # Create new settings
            settings["name"] = "sociar_env"

            result = await current_user.async_db.settings.insert_one(settings)

            if not result.inserted_id:
                raise Exception("Failed to create Sociar settings")

            return SocialMediaResponse(
                success=True,
                message="Sociar API settings created successfully"
            )
    except Exception as e:
        loggers.error(f"Error setting up Sociar API: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to set up Sociar API: {str(e)}")


@router.get("/whatsapp/templates")
async def get_templates(
    page: int = 1,
    rows_per_page: int = 25,
    smm_profile_id: str = "2",
    current_user: UserTenantDB = Depends(get_tenant_info)
) -> SocialMediaResponse:
    try:
        client = await get_sociar_client(current_user)
        templates = client.get_templates(
            smm_profile_id=smm_profile_id,
            page=page,
            rows_per_page=rows_per_page
        )
        return SocialMediaResponse(
            success=True,
            data=templates,
            message="Templates retrieved successfully"
        )
    except Exception as e:
        loggers.error(f"Error getting templates: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get templates: {str(e)}")


@router.post("/whatsapp/templates")
async def create_template(
    template: WhatsAppTemplate,
    current_user: UserTenantDB = Depends(get_tenant_info)
) -> SocialMediaResponse:
    try:
        client = await get_sociar_client(current_user)
        result = client.create_template(template)
        return SocialMediaResponse(
            success=True,
            data=result,
            message="Template created successfully"
        )
    except Exception as e:
        loggers.error(f"Error creating template: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create template: {str(e)}")


@router.get("/whatsapp/message-new-user")
async def message_new_user_endpoint(
    whatsapp_number: str,
    current_user: UserTenantDB = Depends(get_tenant_info)
) -> SocialMediaResponse:
    try:
        result = await send_welcome_message(whatsapp_number, current_user)

        return SocialMediaResponse(
            success=True,
            data=result,
            message="Welcome message sent successfully"
        )
    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error sending welcome message: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to send welcome message: {str(e)}")

