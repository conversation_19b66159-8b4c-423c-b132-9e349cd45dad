
from fastapi import APIRouter, Depends, HTTPException, Body
from typing import Optional
from src.core.security import get_tenant_info
from src.models.user import UserTenantDB
from src.helper.logger import setup_new_logging
from src.v2.external_hooks.social_media_webhooks.client import get_sociar_client
from src.v2.external_hooks.social_media_webhooks.models import (
    SocialMediaMessageRequest,
    SocialMediaResponse
)


loggers = setup_new_logging(__name__)
router = APIRouter(tags=["Sociar APIs"], prefix="/sociar-media")

@router.post("/facebook/message-send")
async def facebook_message_send(
    request: SocialMediaMessageRequest,
    current_user: UserTenantDB = Depends(get_tenant_info)
) -> SocialMediaResponse:
    try:
        client = await get_sociar_client(current_user)
        response = client.send_facebook_message(
            conversation_id=int(request.conversation_id),
            message=request.content
        )
        return SocialMediaResponse(
            success=True,
            data=response,
            message="Facebook message sent successfully"
        )
    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error sending Facebook message: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal Server Error")


@router.post("/instagram/message-send")
async def instagram_message_send(
    request: SocialMediaMessageRequest,
    current_user: UserTenantDB = Depends(get_tenant_info)
) -> SocialMediaResponse:
    try:
        # Get the Sociar client instance
        client = await get_sociar_client(current_user)

        # Send Instagram message using the client
        response = client.send_instagram_message(
            conversation_id=int(request.conversation_id),
            message=request.content
        )

        return SocialMediaResponse(
            success=True,
            data=response,
            message="Instagram message sent successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error sending Instagram message: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal Server Error")


@router.post("/whatsapp/message-send")
async def instagram_message_send(
    request: SocialMediaMessageRequest,
    current_user: UserTenantDB = Depends(get_tenant_info)
) -> SocialMediaResponse:
    try:
        # Get the Sociar client instance
        client = await get_sociar_client(current_user)

        # Send WhatsApp message using the client
        response = client.send_whatsapp_message(
            conversation_id=int(request.conversation_id),
            message=request.content
        )

        return SocialMediaResponse(
            success=True,
            data=response,
            message="Instagram message sent successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error sending Instagram message: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal Server Error")

@router.post("/whatsapp/setup")
async def setup_sociar_api(
    api_token: str = Body(...),
    base_url: Optional[str] = Body("https://new-central-api.sociair.com/api"),
    current_user: UserTenantDB = Depends(get_tenant_info)
) -> SocialMediaResponse:
    """
    Set up Sociar API configuration in the database.

    Args:
        api_token: API token for authentication
        base_url: Base URL for the API
        current_user: Current user with database access

    Returns:
        Success message
    """
    try:
        # Check if settings already exist
        existing_settings = await current_user.async_db.settings.find_one({"name": "sociar_env"})

        settings = {
            "API_TOKEN": api_token,
            "BASE_URL": base_url
        }

        if existing_settings:
            # Update existing settings
            result = await current_user.async_db.settings.update_one(
                {"name": "sociar_env"},
                {"$set": settings}
            )

            if result.modified_count == 0:
                raise Exception("Failed to update Sociar settings")

            return SocialMediaResponse(
                success=True,
                message="Sociar API settings updated successfully"
            )
        else:
            # Create new settings
            settings["name"] = "sociar_env"

            result = await current_user.async_db.settings.insert_one(settings)

            if not result.inserted_id:
                raise Exception("Failed to create Sociar settings")

            return SocialMediaResponse(
                success=True,
                message="Sociar API settings created successfully"
            )
    except Exception as e:
        loggers.error(f"Error setting up Sociar API: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to set up Sociar API: {str(e)}")



