"""
Sociar API client for social media platforms (WhatsApp, Facebook, Instagram).

This module provides a unified client to interact with the Sociar API for
messaging across different social media platforms.
"""

import json
import requests
from typing import Dict, Any, Optional
from src.helper.logger import setup_new_logging
from src.models.user import UserTenantDB
from .models import WhatsAppTemplate

# Initialize logging
loggers = setup_new_logging(__name__)


class SociarClient:
    def __init__(self, slug : str, api_token: str, base_url: str = "https://new-central-api.sociair.com/api"):
        self.api_token = api_token
        self.base_url = base_url
        if slug=="dramit-dev":
            slug="beautyai"
        self.slug=slug
        self._setup_headers()

    def _setup_headers(self) -> None:
        self.common_headers = {
            'Accept': 'application/json, text/plain, /',
            'Accept-Language': 'en-AU,en-GB;q=0.9,en-US;q=0.8,en;q=0.7',
            'Authorization': f'Bearer {self.api_token}',
            'Connection': 'keep-alive',
            'Origin': f'https://{self.slug}.sociair.io',
            'Referer': f'https://{self.slug}.sociair.io',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'cross-site',

        }

    # for WhatsApp
    def get_templates(self, smm_profile_id: str = "2", page: int = 1, rows_per_page: int = 25) -> Dict[str, Any]:
        try:
            filters = json.dumps({"smm_profile_id": smm_profile_id})
            url = f"{self.base_url}/smm/whatsapp-message-templates"
            params = {
                "page": page,
                "rowsPerPage": rows_per_page,
                "query": "",
                "filters": filters,
                "descending": "true",
                "sortBy": "id",
                "view": "table"
            }

            response = requests.get(url, headers=self.common_headers, params=params)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            loggers.error(f"Error fetching templates: {str(e)}")
            raise Exception(f"Failed to fetch templates: {str(e)}")

    def create_template(self, template: WhatsAppTemplate) -> Dict[str, Any]:
        try:
            url = f"{self.base_url}/smm/whatsapp-message-templates"
            headers = self.common_headers.copy()
            # Remove the Content-Type header - requests will set it correctly with the boundary
            if 'Content-Type' in headers:
                del headers['Content-Type']
            loggers.info(f"Creating template with data: {template.model_dump()}")

            files = {
                'smm_profile_id': (None, template.smm_profile_id),
                'language': (None, template.language),
                'header_type': (None, template.header_type),
                'template_type': (None, template.template_type),
                'name': (None, template.name),
                'body_text': (None, template.body_text),
            }

            if template.header_text:
                files['header_text'] = (None, template.header_text)
            if template.footer_text:
                files['footer_text'] = (None, template.footer_text)

            # Debug log the request details
            loggers.info(f"Sending request to: {url}")
            loggers.info(f"With files: {files}")

            response = requests.post(url, headers=headers, files=files)

            # Log the response for debugging
            loggers.info(f"Response status code: {response.status_code}")
            loggers.info(f"Response content: {response.text[:500]}...")  # Log first 500 chars to avoid huge logs

            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            loggers.error(f"Error creating template: {str(e)}")
            # If we have response content, log it for debugging
            if hasattr(e, 'response') and e.response is not None:
                loggers.error(f"Response content: {e.response.text}")
            raise Exception(f"Failed to create template: {str(e)}")

    def send_whatsapp_new_user_message(self, conversation_id: int, template_id: int, to_number: str, variables: dict = None) -> Dict[str, Any]:
        url = f"{self.base_url}/smm/whatsapp/conversations/{conversation_id}/send-new-message"

        payload = {
            "template_id": template_id,
            "to_number": to_number
        }
        
        # Add template variables if provided
        if variables and isinstance(variables, dict):
            payload["variables"] = variables
            loggers.info(f"Sending message with variables: {variables}")

        try:
            loggers.info(f"Sending WhatsApp message to {to_number} using template {template_id}")
            response = requests.post(url, headers=self.common_headers, json=payload)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            loggers.error(f"Failed to send WhatsApp message: {str(e)}")
            if hasattr(e, 'response') and e.response is not None:
                loggers.error(f"Response content: {e.response.text}")
            raise Exception(f"API request failed: {str(e)}")
        
    def send_whatsapp_message(self, conversation_id: int, message: str) -> Dict[str, Any]:
        url = f"{self.base_url}/smm/whatsapp/conversations/{conversation_id}/send-message"

        payload = {}
        if message.strip():
            payload["content"] = message.strip()

        if not payload:
            raise ValueError("Cannot send Facebook message: content and attachments are both empty.")
        try:
            loggers.info(f"Sending WhatsApp message to {conversation_id} ")
            response = requests.post(url, headers=self.common_headers, json=payload)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            loggers.error(f"Failed to send WhatsApp message: {str(e)}")
            if hasattr(e, 'response') and e.response is not None:
                loggers.error(f"Response content: {e.response.text}")
            raise Exception(f"API request failed: {str(e)}")

    # for Facebook 
    def send_facebook_message(self, conversation_id: int, message: str):
        url = f"{self.base_url}/smm/fb_page/conversations/{conversation_id}/send-message"
        
        payload = {}
        if message.strip():
            payload["content"] = message.strip()

        if not payload:
            raise ValueError("Cannot send Facebook message: content and attachments are both empty.")

        loggers.info(f"Sending Facebook message via profile {conversation_id}")
        response = requests.post(url, headers=self.common_headers, json=payload)
        try:
            response.raise_for_status()
        except requests.exceptions.HTTPError as e:
            loggers.error(f"Response content: {response.text}")
            raise Exception(f"API request failed: {str(e)}")

        return response.json()


    # for Instagram 
    def send_instagram_message(self, conversation_id: int, message: str) -> Dict[str, Any]:

        url = f"{self.base_url}/smm/instagram/conversations/{conversation_id}/send-message"
        payload = {}
        if message.strip():
            payload["content"] = message.strip()

        if not payload:
            raise ValueError("Cannot send Facebook message: content and attachments are both empty.")
        try:
            loggers.info(f"Sending Instagram message via profile {conversation_id}")
            response = requests.post(url, headers=self.common_headers, data=payload)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            loggers.error(f"Failed to send Instagram message: {str(e)}")
            if hasattr(e, 'response') and e.response is not None:
                loggers.error(f"Response content: {e.response.text}")
            raise Exception(f"API request failed: {str(e)}")

    # # Legacy method names for backward compatibility
    # def send_message(self, profile_id: int, template_id: int, to_number: str, variables: dict = None) -> Dict[str, Any]:
    #     """Legacy method name for WhatsApp message sending."""
    #     return self.send_whatsapp_message(profile_id, template_id, to_number, variables)

    # def facebook_send_message(self, profile_id: int, content: str) -> Dict[str, Any]:
    #     """Legacy method name for Facebook message sending."""
    #     return self.send_facebook_message(profile_id, content)

    # def instagram_send_message(self, profile_id: int, content: str) -> Dict[str, Any]:
    #     """Legacy method name for Instagram message sending."""
    #     return self.send_instagram_message(profile_id, content)


async def get_sociar_client(current_user: UserTenantDB):
    slug=current_user.slug
    try:
        settings = await current_user.async_db.settings.find_one({"name": "sociar_env"})

        if not settings:
            loggers.error("Sociar settings not found in database")
            raise Exception("Sociar settings not found in database. Please configure Sociar settings first.")
        api_token = settings.get("API_TOKEN")
        base_url = settings.get("BASE_URL", "https://new-central-api.sociair.com/api")

        if not api_token:
            raise Exception("API token not found in Sociar settings")

        return SociarClient(api_token=api_token, base_url=base_url,slug=slug)
    except Exception as e:
        loggers.error(f"Error initializing Sociar client: {str(e)}")
        raise Exception(f"Failed to initialize Sociar client: {str(e)}")
