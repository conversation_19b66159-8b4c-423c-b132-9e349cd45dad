# from typing import Dict, Any
# from fastapi import HTTPException
# from src.models.user import UserTenantDB
# from src.helper.logger import setup_new_logging
# from .client import get_sociar_client

# # Initialize logging
# loggers = setup_new_logging(__name__)


# async def send_facebook_message(profile_id: str, content: str, current_user: UserTenantDB) -> Dict[str, Any]:
#     try:
#         client = await get_sociar_client(current_user)
        
#         if not profile_id or not content:
#             raise HTTPException(status_code=400, detail="Missing 'profile_id' or 'content'.")
            
#         # Call client method
#         response = client.send_facebook_message(int(profile_id), content)
        
#         return response
        
#     except HTTPException:
#         raise
#     except Exception as e:
#         loggers.error(f"Error sending Facebook message: {str(e)}")
#         raise HTTPException(status_code=500, detail="Internal Server Error")


# async def send_instagram_message(profile_id: str, content: str, current_user: UserTenantDB) -> Dict[str, Any]:
#     try:
#         client = await get_sociar_client(current_user)
        
#         if not profile_id or not content:
#             raise HTTPException(status_code=400, detail="Missing 'profile_id' or 'content'.")
            
#         # Call client method
#         response = client.send_instagram_message(int(profile_id), content)
        
#         return response
        
#     except HTTPException:
#         raise
#     except Exception as e:
#         loggers.error(f"Error sending Instagram message: {str(e)}")
#         raise HTTPException(status_code=500, detail="Internal Server Error")


# async def send_whatsapp_message(
#     profile_id: int, 
#     template_id: int, 
#     to_number: str, 
#     current_user: UserTenantDB,
#     variables: Dict[str, str] = None
# ) -> Dict[str, Any]:
#     try:
#         client = await get_sociar_client(current_user)
        
#         if not profile_id or not template_id or not to_number:
#             raise HTTPException(status_code=400, detail="Missing required parameters.")
            
#         # Call client method
#         response = client.send_whatsapp_message(profile_id, template_id, to_number, variables)
        
#         return response
        
#     except HTTPException:
#         raise
#     except Exception as e:
#         loggers.error(f"Error sending WhatsApp message: {str(e)}")
#         raise HTTPException(status_code=500, detail="Internal Server Error")


# async def send_welcome_message(whatsapp_number: str, current_user: UserTenantDB) -> Dict[str, Any]:
#     try:
#         clean_number = whatsapp_number
#         client = await get_sociar_client(current_user)

#         result = client.send_whatsapp_message(
#             profile_id=2,  # Example profile ID
#             template_id=3,  # Welcome message template ID
#             to_number=clean_number
#         )
        
#         return result
        
#     except HTTPException:
#         raise
#     except Exception as e:
#         loggers.error(f"Error sending welcome message: {str(e)}")
#         raise HTTPException(status_code=500, detail=f"Failed to send welcome message: {str(e)}")
