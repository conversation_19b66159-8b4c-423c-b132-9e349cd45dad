"""
Message utilities for OpenAI streaming chat.

This module provides functions to prepare and format messages
for OpenAI API calls, including language detection and product identification.
"""
from typing import Dict, Any, List,Optional

from src.helper.logger import setup_new_logging
from src.models.user import UserTenantDB
from src.models.chat_hist import ChatHistMessage
from src.streamlitdemo.chatdemo.identify_product import identify_product
from src.streamlitdemo.chatdemo.senti_lang import detect_language

# Initialize logging
loggers = setup_new_logging(__name__)


async def detect_language_and_product(message_content: str, current_user: UserTenantDB) -> Dict[str, Any]:
    """
    Detect language and identify product from user message.

    Args:
        message_content: The content of the user's message
        current_user: Current user tenant database

    Returns:
        Dictionary containing language, product, and usage information
    """
    # Get language detection and product identification prompts
    detect_language_prompt = current_user.db.prompt.find_one({"name": "language_detection"})
    identify_product_prompt = current_user.db.prompt.find_one({"name": "identify_product"})

    # Perform language detection and product identification
    identified_product, prod_usage = identify_product({
        "latest_message_str": message_content,
        "prompt": identify_product_prompt
    })
    language, lang_usage = detect_language({
        "latest_message_str": message_content,
        "prompt": detect_language_prompt
    })

    return {
        "language": language,
        "identified_product": identified_product,
        "lang_usage": lang_usage,
        "prod_usage": prod_usage
    }


async def get_system_prompt(current_user: UserTenantDB, identified_product: str, language: str,mode:Optional[str]) -> Dict[str, Any]:
    """
    Get and format system prompt from database.

    Args:
        current_user: Current user tenant database
        identified_product: Identified product from user message
        language: Detected language from user message

    Returns:
        Dictionary containing formatted system prompt and model information
    """
    # Get system prompt from database
    if mode == "simplified":
        sys_prompt = current_user.db.prompt.find_one({"name": "reply_prompt_openai_simplified"})
    elif mode == "elaborated":
        sys_prompt = current_user.db.prompt.find_one({"name": "reply_prompt_openai_elaborated"})
    else:
        sys_prompt = current_user.db.prompt.find_one({"name": "reply_prompt_openai"})
    if not sys_prompt:
        return None

    # Format system prompt with detected information
    system_prompt_text = sys_prompt["text"].format(
        identified_product=identified_product,
        language=language,
        response_mode_str="Provide a detailed and explained answer in a couple of sentences."
    )

    return {
        "text": system_prompt_text,
        "model": sys_prompt["model"]
    }


async def prepare_messages(system_prompt_text: str, chat_hist: List[ChatHistMessage],
                          latest_message: ChatHistMessage) -> List[Dict[str, str]]:
    """
    Prepare messages for OpenAI API call.

    Args:
        system_prompt_text: Formatted system prompt text
        chat_hist: Chat history messages
        latest_message: Latest user message

    Returns:
        List of formatted messages for OpenAI API
    """
    # Initialize messages list with system prompt
    messages = [{"role": "system", "content": system_prompt_text}]

    # Add latest message content AFTER chat history
    # Add chat history if available (oldest messages first)
    if chat_hist:
        messages.extend([msg.model_dump_promp() for msg in chat_hist])

    messages.append({
        "role": "user",
        "content": latest_message.content
    })

    # Add media values if available
    if latest_message.media_values:
        loggers.debug(f"Message Media Values: {latest_message.media_values}")
        messages.append({
            "role": "user",
            "content": f"Description of User Provided Images:\n{latest_message.media_values}"
        })

    loggers.info(f"Prepared {len(messages)} messages for OpenAI API")
    loggers.info(f"Latest message content: {latest_message.content}")

    from pprint import pprint
    pprint(messages)
    return messages
