from fastapi import APIRouter,Depends,Query
from fastapi.responses import JSONResponse
from src.core.security import get_tenant_info
from src.models.user import UserTenantDB
from typing import List,Optional
from src.v2.KB.qdrant.qdrant_client import Qdrant_Call,QdrantConfig
from src.helper.logger import setup_new_logging

loggers=setup_new_logging(__name__)


from src.reply.minio_client import MinIOClient, MinIOConfig
from dotenv import load_dotenv
load_dotenv()




router = APIRouter(tags=["file Setup"])

@router.get("/list_docs")
async def list_docs(
    prefix: Optional[str] = "Files",
    page: int = Query(1, ge=1),  # Default page = 1
    limit: int = Query(5, ge=1),  # Default limit = 5
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    try:
        minio_config = current_user.db.settings.find_one({"name": "env"}).get("minio_config")
    except Exception as e:
        raise Exception(f"Minio Error {e}") from e

    config = MinIOConfig(
        minio_url=minio_config.get("minio_url"),
        access_key=minio_config.get("access_key"),
        secret_key=minio_config.get("secret_key"),
        bucket_name=minio_config.get("bucket_name"),
    )
    minio_client = MinIOClient(config)

    minio_files = minio_client.list_files(bucket_name=minio_config.get("bucket_name"), prefix=prefix)
    cleaned_files = [file.replace("Files/", "", 1) for file in minio_files]

    # Paginate results
    start = (page - 1) * limit
    end = start + limit
    paginated_files = cleaned_files[start:end]

    return {
        "files": paginated_files,
        "page": page,
        "limit": limit,
        "total_files": len(cleaned_files),
        "total_pages": (len(cleaned_files) + limit - 1) // limit  # Calculate total pages
    }

    

 

    
from urllib.parse import unquote

@router.get("/get_doc/{filename:path}")  # ":path" allows slashes in filename
async def get_doc(filename: str, current_user: UserTenantDB = Depends(get_tenant_info)):
    filename = unquote(filename)  # Decode URL-encoded characters

    if "/" in filename:
        folderName, newFileName = filename.rsplit("/", 1)
    else:
        folderName, newFileName = None, filename
        # raise Exception("folder name missing")


    try:
        minio_config = current_user.db.settings.find_one({"name": "env"}).get("minio_config")

        config = MinIOConfig(
            minio_url=minio_config.get("minio_url"),
            access_key=minio_config.get("access_key"),
            secret_key=minio_config.get("secret_key"),
            bucket_name=minio_config.get("bucket_name"),
        )

        minio_client = MinIOClient(config)

        # Generate pre-signed URL if the file exists
        presigned_url = minio_client.get_presigned_url(newFileName, folder=folderName)

        return {"file_name": newFileName, "presigned_url": presigned_url}

    except FileNotFoundError as e:
        return {"error": str(e)}

    except Exception as e:
        return {"error": f"Unexpected error: {e}"}

@router.delete("/delete_file/{filename}")
async def delete_file(filename:str,current_user:UserTenantDB=Depends(get_tenant_info)):


    loggers.info(f"Deleting file: {filename} from current_user: {current_user.user.id}")
    try:
        minio_config: dict = current_user.db.settings.find_one({"name": "env"}).get("minio_config")

        minio = MinIOClient(config=MinIOConfig(**minio_config))
        minio_client = minio.client_()

        # Define the file paths for the PDF and associated images
        pdf_file_path = f"Files/{filename}"
        image_folder_path = f"Images/{filename}"
        loggers.info(f"Deleting file: {pdf_file_path} from current_user: {current_user.user.id}")
        loggers.info(f"Deleting file: {image_folder_path} from current_user: {current_user.user.id}")
        minio_client.remove_object(minio_config.get("bucket_name"), pdf_file_path)
         # List all objects under the "Images/{filename}/" prefix
        objects_to_delete = minio_client.list_objects(minio_config.get("bucket_name"), prefix=image_folder_path, recursive=True)

        # Delete all objects in the folder
        from minio.deleteobjects import DeleteObject
        delete_objects = [DeleteObject(obj.object_name) for obj in objects_to_delete]
        if delete_objects:
            loggers.info(f"Deleting {len(delete_objects)} objects...")
            delete_errors = list(minio_client.remove_objects(minio_config.get("bucket_name"), delete_objects))
            loggers.info(f"Deleted {delete_errors} objects.")
        loggers.info(f"Deleted all files in {image_folder_path}")
        
    except Exception as e:
        raise 
    
    try:
        qd_config = current_user.db.settings.find_one({"name": "env"}).get("qdrant_config")

        page_collection = qd_config.get("page_collection")
        sentence_collection = qd_config.get("sentence_collection")
        sentence_split_collection = qd_config.get("sentence_split_collection")

        config = QdrantConfig(
            host=qd_config["host"],
            port=qd_config["port"],
            coll_name=qd_config.get("page_collection")
        )
        
        qd = Qdrant_Call(config=config).client_()
        
        #delete file with this metadata for page_collection,sentence_collection and sentence_with_context
        from qdrant_client.http import models as rest
        
        filter=rest.Filter(**{
            "must":{
                "key":"source",
                "match":{
                    "value":filename
                }
            }
        })
        qd.delete(points_selector=filter,collection_name=page_collection)
        qd.delete(points_selector=filter,collection_name=sentence_split_collection)
        qd.delete(points_selector=filter,collection_name=sentence_collection)
        
    except Exception as e:
        loggers.error(f"deleteing from qdrant: {e}")
    
    
    return JSONResponse(status_code=200, content="File Deleted")

