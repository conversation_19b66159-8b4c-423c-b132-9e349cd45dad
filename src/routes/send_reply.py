from fastapi import APIRout<PERSON>, Depends, HTTPException
from src.models.user import UserTenantDB
from src.core.security import get_tenant_info
from bson import ObjectId
from src.v2.external_hooks.whatsapp_webhook.send_message import send_whatsapp_message
from pydantic import BaseModel
from typing import List, Dict, Any, Optional, Literal
from pydantic import BaseModel, field_validator, model_validator
from src.reply.minio_client import MinIOClient, MinIOConfig
from src.models.credit import CreditManager, get_credit_info
from src.helper import logger

loggers = logger.setup_new_logging(__name__)

router = APIRouter( tags=["Send Reply"])

class send_reply_(BaseModel):
    user_id: str
    message: Optional[str]=None
    ai_response_id: Optional[str] = None
    media_url: Optional[List[Dict[str, Any]]] = None
    channel: Literal["whatsapp","website"] = "whatsapp"

    @model_validator(mode="before")
    def validate_message(cls, values):
        # Ensure 'message' and 'media_url' are in 'values'
        message = values.get('message', "")
        if message:
            message = message.strip()
        media_urls = values.get('media_url', [])

        return values

    @model_validator(mode="after")
    def move_youtube_links_to_message(cls, values):
        media = values.media_url or []  # Access the attribute directly, with fallback to empty list
        updated_media = []
        youtube_links = []
        
        for item in media:
            url = item.get("url") or item.get("link") or ""
            if "www.youtube.com" in url:
                youtube_links.append(url)
            else:
                updated_media.append(item)
        
        # Update message with YouTube links and filter out YouTube media
        if youtube_links:
            combined_links = "\n".join(youtube_links)
            values.message = f"{values.message.strip()}\n{combined_links}"
            values.media_url = updated_media if updated_media else None
        
        return values
    def set_message(self, new_message: str):
        """Safely update message and re-run validation logic."""
        data = self.model_dump()
        data['message'] = new_message
        validated = send_reply_.model_validate(data)
        self.message = validated.message
        self.media_url = validated.media_url
    class Config:
        from_attributes = True
        json_encoders = {
            ObjectId: str
        }

    
@router.post("/send_agent_reply")
async def send_agent_reply(
    request: send_reply_,   

    current_user: UserTenantDB = Depends(get_tenant_info),
) -> str:
    try:
        credits_manager = CreditManager(current_user.db)
        per_cost, remaining_credit = get_credit_info( cost_type="whatsapp_msg_cost",current_user=current_user)
        if remaining_credit < per_cost:
            raise HTTPException(status_code=402, detail="Insufficient credits")
        if request.channel != "whatsapp":
            raise HTTPException(status_code=501, detail="Can Only send reply to Whatsapp Channel")
            
        latest_response=current_user.db.ai_response.find_one({"request.user_id":request.user_id},sort=[("created_at", -1)])
        request.ai_response_id = latest_response["_id"]
        env_var=current_user.db.settings.find_one({"name": "env"})
        minio_config=env_var.get("minio_config")
        minio = MinIOClient(config=MinIOConfig(access_key=minio_config.get("access_key"),secret_key=minio_config.get("secret_key"),minio_url=minio_config.get("minio_url"),bucket_name=minio_config.get("bucket_name")))

        send_msg=await send_whatsapp_message(
                request,minio, current_user
            )
        credit_result = credits_manager.deduct_credits(
                amount=per_cost,
                description="Whatsapp Message Reply",
                message_id=request.ai_response_id,
            )
        if not credit_result["success"]:
           loggers.error(f"Failed to deduct credits send reply: {credit_result['message']}")
        return send_msg
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
