try:
    import streamlit as st
except Exception as e:
    import os
    os.system("pip install streamlit")
finally:
    import streamlit

# import sys
# sys.path.append("/Users/<USER>/Documents/echo_bot-1/src")

import src

from src.streamlitdemo.chatdemo.respond import generate_response, MsgRequest, calculate_summary
# Configure the page layout
st.set_page_config(page_title="Chat", layout="wide")

# Initialize session state for chat messages if not already present
if "messages" not in st.session_state:
    st.session_state["messages"] = []
if "previous_summary" not in st.session_state:
    st.session_state["previous_summary"] = "This is a New Conversation. "

# Define three columns: left for chat, middle for document management, right for TOC

# ------------------------
# Left Column: Enhanced Chat Interface
# ------------------------
st.header("Chat Interface")

# Display chat history using experimental chat components if available
# Each message is stored as a dictionary with 'role' and 'content'
for message in st.session_state["messages"]:
    if message["role"] == "user":
        st.chat_message("user").write(message["content"])
    else:
        st.chat_message("assistant").write(message["content"])

# Chat input: When the user sends a message, it's appended to the session state,
# and a placeholder AI response is generated.
user_input = st.chat_input("Type your message here")
if user_input:
    # Append user message
    st.session_state["messages"].append({"role": "user", "content": user_input})

    # Here, you would normally process the input with an AI model.
    # For this example, we'll simulate a response.
    # ai_response = "This is a placeholder response from the AI."

    print(f'xx***{st.session_state["previous_summary"]}***xx')

    ai_response = generate_response(MsgRequest(customer_id=0, message=user_input, previous_summary=st.session_state["previous_summary"]))
    st.session_state["messages"].append({"role": "assistant", "content": ai_response})

    st.session_state["previous_summary"] = calculate_summary(
        st.session_state["previous_summary"],
        user_message=user_input,
        ai_response=ai_response,
    )


    # Rerun to update the chat interface with the new messages
    st.rerun()
