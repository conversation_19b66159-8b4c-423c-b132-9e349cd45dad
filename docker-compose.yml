version: '3.8'
services:
  traefik:
    image: traefik:v2.10
    command:
      - "--api.insecure=true"
      - "--api.dashboard=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--providers.docker.watch=true"
      - "--entrypoints.web.address=:8201"
      - "--log.level=INFO"
      - "--accesslog=true"
      - "--accesslog.format=json"
      - "--accesslog.fields.names.ClientUsername=drop"
      - "--accesslog.fields.headers.names.Authorization=drop"
      - "--accesslog.fields.headers.names.Content-Type=keep"
      - "--metrics.prometheus=true"
      - "--ping=true"
      - "--global.sendAnonymousUsage=false"
    ports:
      - "8201:8201" # Main web port for API
      - "8202:8080" # Traefik dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - traefik
    healthcheck:
      test: ["CMD", "traefik", "healthcheck", "--ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  app:
    image: eko-backend:latest
    networks:
      - traefik
    environment:
      - PORT=8000
      - WORKERS=1
      - SERVICE_NAME=eko-backend
    # healthcheck:
    #   test: ["CMD", "python", "-c", "import urllib.request; urllib.request.urlopen('http://localhost:8000/health')"]
    #   interval: 30s
    #   timeout: 10s
    #   retries: 3
    #   start_period: 60s
    restart: unless-stopped
    deploy:
      replicas: 3
    labels:
      - "traefik.enable=true"

      # Main router for the application
      - "traefik.http.routers.app.rule=PathPrefix(`/`)"
      - "traefik.http.routers.app.entrypoints=web"
      - "traefik.http.routers.app.service=app"
      - "traefik.http.routers.app.priority=1"
      - "traefik.http.routers.app.middlewares=trace-headers"

      # Service configuration with detailed naming
      - "traefik.http.services.app.loadbalancer.server.port=8000"
      - "traefik.http.services.app.loadbalancer.server.scheme=http"

      # Simple tracing - add container identification
      - "traefik.http.middlewares.trace-headers.headers.customresponseheaders.X-Load-Balancer=traefik"

      # Add instance identification
      - "traefik.docker.network=echo_bot_traefik"

      # WebSocket support - let Traefik handle WebSocket upgrades automatically
      # No custom headers needed - Traefik will detect WebSocket upgrade requests

      # Load balancing configuration
      - "traefik.http.services.app.loadbalancer.passhostheader=true"
      - "traefik.http.services.app.loadbalancer.responseforwarding.flushinterval=1ms"

      # Sticky sessions for WebSocket connections
      - "traefik.http.services.app.loadbalancer.sticky.cookie=true"
      - "traefik.http.services.app.loadbalancer.sticky.cookie.name=eko-server-id"
      - "traefik.http.services.app.loadbalancer.sticky.cookie.secure=false"
      - "traefik.http.services.app.loadbalancer.sticky.cookie.httponly=true"
      - "traefik.http.services.app.loadbalancer.sticky.cookie.samesite=lax"

      # Health check configuration (disabled for now)
      # - "traefik.http.services.app.loadbalancer.healthcheck.path=/health"
      # - "traefik.http.services.app.loadbalancer.healthcheck.interval=30s"
      # - "traefik.http.services.app.loadbalancer.healthcheck.timeout=10s"

networks:
  traefik:
    driver: bridge